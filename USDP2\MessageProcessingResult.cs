namespace USDP2
{
    /// <summary>
    /// Represents the result of message processing operations in network receivers.
    /// Provides information about whether the processing was successful and any associated messages.
    /// </summary>
    public readonly struct MessageProcessingResult
    {
        /// <summary>
        /// Gets a value indicating whether the message processing was successful.
        /// </summary>
        public bool Success { get; }

        /// <summary>
        /// Gets the message describing the result of the processing operation.
        /// This can contain success confirmation or error details.
        /// </summary>
        public string Message { get; }

        /// <summary>
        /// Initializes a new instance of the <see cref="MessageProcessingResult"/> struct.
        /// </summary>
        /// <param name="success">Whether the processing was successful.</param>
        /// <param name="message">The message describing the result.</param>
        public MessageProcessingResult(bool success, string message)
        {
            Success = success;
            Message = message ?? string.Empty;
        }

        /// <summary>
        /// Creates a successful processing result.
        /// </summary>
        /// <param name="message">Optional success message.</param>
        /// <returns>A successful MessageProcessingResult.</returns>
        public static MessageProcessingResult CreateSuccess(string message = "Message processed successfully")
        {
            return new MessageProcessingResult(true, message);
        }

        /// <summary>
        /// Creates a failed processing result.
        /// </summary>
        /// <param name="message">The error message.</param>
        /// <returns>A failed MessageProcessingResult.</returns>
        public static MessageProcessingResult CreateFailure(string message)
        {
            return new MessageProcessingResult(false, message);
        }

        /// <summary>
        /// Returns a string representation of the processing result.
        /// </summary>
        /// <returns>A string describing the result.</returns>
        public override string ToString()
        {
            return $"{(Success ? "Success" : "Failure")}: {Message}";
        }

        /// <summary>
        /// Implicitly converts a boolean to a MessageProcessingResult.
        /// </summary>
        /// <param name="success">The success value.</param>
        /// <returns>A MessageProcessingResult with the specified success value.</returns>
        public static implicit operator MessageProcessingResult(bool success)
        {
            return success 
                ? CreateSuccess() 
                : CreateFailure("Operation failed");
        }

        /// <summary>
        /// Implicitly converts a MessageProcessingResult to a boolean.
        /// </summary>
        /// <param name="result">The MessageProcessingResult.</param>
        /// <returns>The success value of the result.</returns>
        public static implicit operator bool(MessageProcessingResult result)
        {
            return result.Success;
        }
    }
}
