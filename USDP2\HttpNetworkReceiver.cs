using System;
using System.Buffers;
using System.Net;
using System.Threading;
using System.Threading.Tasks;

namespace USDP2
{
    /// <summary>
    /// The http network receiver.
    /// </summary>
    public class HttpNetworkReceiver : INetworkReceiver
    {
        /// <summary>
        /// The listener.
        /// </summary>
        private readonly HttpListener _listener;

        /// <summary>
        /// Initializes a new instance of the <see cref="HttpNetworkReceiver"/> class.
        /// </summary>
        /// <param name="port">The port to listen on. Must be between 1 and 65535.</param>
        /// <exception cref="ArgumentOutOfRangeException">Thrown when port is outside the valid range.</exception>
        public HttpNetworkReceiver(int port)
        {
            // Validate port before attempting to create HTTP listener
            InputValidator.ValidatePort(port, nameof(port));

            _listener = new HttpListener();
            var path = UsdpConfiguration.Instance.HttpEndpointPath;
            _listener.Prefixes.Add($"http://+:{port}{path}/");
        }

        /// <summary>
        /// Start the receiving asynchronously.
        /// </summary>
        /// <param name="onMessageReceived">The on message received.</param>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns>A <see cref="Task"/></returns>

        public async Task StartReceivingAsync(Func<byte[], string, int, Task> onMessageReceived, CancellationToken cancellationToken = default)
        {
            _listener.Start();
            var bufferPool = ArrayPool<byte>.Shared;

            try
            {
                while (!cancellationToken.IsCancellationRequested)
                {
                    var context = await _listener.GetContextAsync().ConfigureAwait(false);
                    var request = context.Request;
                    using var response = context.Response;

                    // Validate and limit buffer size to prevent overflow and memory exhaustion
                    const int MaxBufferSize = 1024 * 1024; // 1MB limit
                    var contentLength = request.ContentLength64;

                    // Validate content length
                    if (contentLength < 0)
                    {
                        response.StatusCode = 400; // Bad Request
                        response.Close();
                        continue;
                    }

                    // Prevent integer overflow and limit maximum size
                    var bufferSize = (int)Math.Min(Math.Min(contentLength, MaxBufferSize), int.MaxValue);
                    var buffer = bufferPool.Rent(bufferSize);
                    try
                    {
                        int bytesRead = await request.InputStream.ReadAsync(buffer.AsMemory(0, bufferSize), cancellationToken).ConfigureAwait(false);

                        var remoteIp = context.Request.RemoteEndPoint?.Address.ToString() ?? "";
                        var remotePort = context.Request.RemoteEndPoint?.Port ?? 0;

                        // Process the received message with optimized buffer usage
                        var processingResult = await ProcessReceivedMessageAsync(
                            buffer,
                            bytesRead,
                            remoteIp,
                            remotePort,
                            onMessageReceived).ConfigureAwait(false);

                        response.StatusCode = processingResult.Success ? 200 : 400;
                    }
                    finally
                    {
                        bufferPool.Return(buffer);
                    }

                    response.Close();
                }
            }
            finally
            {
                _listener.Stop();
            }
        }

        /// <summary>
        /// Processes a received message with comprehensive error handling and logging.
        /// Optimized to minimize memory allocations by working directly with the buffer.
        /// </summary>
        /// <param name="buffer">The buffer containing the received message data.</param>
        /// <param name="messageLength">The length of the actual message data in the buffer.</param>
        /// <param name="remoteIp">The remote IP address.</param>
        /// <param name="remotePort">The remote port.</param>
        /// <param name="onMessageReceived">The message processing callback.</param>
        /// <returns>A processing result indicating success or failure.</returns>
        private static async Task<MessageProcessingResult> ProcessReceivedMessageAsync(
            byte[] buffer,
            int messageLength,
            string remoteIp,
            int remotePort,
            Func<byte[], string, int, Task> onMessageReceived)
        {
            try
            {
                // Validate input parameters
                if (buffer == null || messageLength <= 0)
                {
                    Diagnostics.Log("MessageProcessingError", new
                    {
                        RemoteAddress = remoteIp,
                        RemotePort = remotePort,
                        Error = "Received empty or null message data",
                        ErrorType = "ValidationError"
                    });
                    return new MessageProcessingResult(false, "Empty message data");
                }

                if (string.IsNullOrEmpty(remoteIp))
                {
                    Diagnostics.Log("MessageProcessingError", new
                    {
                        RemoteAddress = remoteIp,
                        RemotePort = remotePort,
                        Error = "Invalid remote IP address",
                        ErrorType = "ValidationError"
                    });
                    return new MessageProcessingResult(false, "Invalid remote IP");
                }

                // Create a properly sized array only when needed for the callback
                // This is the minimal allocation required to maintain interface compatibility
                var messageData = new byte[messageLength];
                buffer.AsSpan(0, messageLength).CopyTo(messageData);

                // Process the message
                await onMessageReceived(messageData, remoteIp, remotePort).ConfigureAwait(false);

                return new MessageProcessingResult(true, "Message processed successfully");
            }
            catch (ArgumentException ex)
            {
                // Handle argument validation errors
                Diagnostics.Log("MessageProcessingError", new
                {
                    RemoteAddress = remoteIp,
                    RemotePort = remotePort,
                    Error = ex.Message,
                    ErrorType = "ArgumentError",
                    StackTrace = ex.StackTrace
                });
                return new MessageProcessingResult(false, $"Argument error: {ex.Message}");
            }
            catch (InvalidOperationException ex)
            {
                // Handle state-related errors
                Diagnostics.Log("MessageProcessingError", new
                {
                    RemoteAddress = remoteIp,
                    RemotePort = remotePort,
                    Error = ex.Message,
                    ErrorType = "StateError",
                    StackTrace = ex.StackTrace
                });
                return new MessageProcessingResult(false, $"State error: {ex.Message}");
            }
            catch (Exception ex)
            {
                // Handle all other unexpected errors
                Diagnostics.Log("MessageProcessingError", new
                {
                    RemoteAddress = remoteIp,
                    RemotePort = remotePort,
                    Error = ex.Message,
                    ErrorType = "UnexpectedError",
                    ExceptionType = ex.GetType().Name,
                    StackTrace = ex.StackTrace
                });
                return new MessageProcessingResult(false, $"Unexpected error: {ex.Message}");
            }
        }



        /// <summary>
        /// TODO: Add Summary.
        /// </summary>
        /// <returns>A <see cref="ValueTask"/></returns>
        public ValueTask DisposeAsync()
        {
            _listener.Close();
            GC.SuppressFinalize(this);
            return ValueTask.CompletedTask;
        }
    }
}