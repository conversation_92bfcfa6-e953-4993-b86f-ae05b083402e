using System;
using System.Threading;
using System.Threading.Tasks;

namespace USDP2
{
    /// <summary>
    /// Enhanced network receiver interface that supports high-performance, zero-allocation message processing.
    /// This interface provides both backward compatibility and modern span-based APIs for optimal performance.
    /// </summary>
    public interface IOptimizedNetworkReceiver : INetworkReceiver
    {
        /// <summary>
        /// Starts receiving messages asynchronously with optimized callback that minimizes allocations.
        /// This method provides better performance by using Memory&lt;byte&gt; instead of arrays.
        /// </summary>
        /// <param name="onMessageReceived">
        /// Callback function that receives a Memory&lt;byte&gt; containing the message data.
        /// The memory is valid during the callback execution and should not be stored beyond that.
        /// </param>
        /// <param name="cancellationToken">Cancellation token to stop receiving.</param>
        /// <returns>A task representing the asynchronous receiving operation.</returns>
        /// <remarks>
        /// This method provides optimized message processing by avoiding unnecessary array copies.
        /// The callback receives a Memory&lt;byte&gt; that can be efficiently processed.
        ///
        /// Performance benefits:
        /// - Reduced heap allocations for message data
        /// - Efficient memory usage with buffer pooling
        /// - Reduced GC pressure
        /// - Better performance than array-based approaches
        ///
        /// Usage example:
        /// <code>
        /// await receiver.StartReceivingOptimizedAsync(
        ///     async (messageMemory, remoteIp, remotePort) =>
        ///     {
        ///         // Process the message efficiently
        ///         var message = Encoding.UTF8.GetString(messageMemory.Span);
        ///         await ProcessMessageAsync(message, remoteIp, remotePort);
        ///     },
        ///     cancellationToken);
        /// </code>
        /// </remarks>
        Task StartReceivingOptimizedAsync(
            Func<Memory<byte>, string, int, Task> onMessageReceived,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Starts receiving messages asynchronously with optimized callback and additional metadata.
        /// </summary>
        /// <param name="onMessageReceived">
        /// Callback function that receives message data and metadata.
        /// </param>
        /// <param name="cancellationToken">Cancellation token to stop receiving.</param>
        /// <returns>A task representing the asynchronous receiving operation.</returns>
        Task StartReceivingOptimizedAsync(
            Func<Memory<byte>, NetworkMessageMetadata, Task> onMessageReceived,
            CancellationToken cancellationToken = default);
    }

    /// <summary>
    /// Metadata associated with a received network message.
    /// Provides additional context about the message source and processing.
    /// </summary>
    public readonly struct NetworkMessageMetadata
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="NetworkMessageMetadata"/> struct.
        /// </summary>
        /// <param name="remoteIp">The remote IP address.</param>
        /// <param name="remotePort">The remote port number.</param>
        /// <param name="receivedAt">The timestamp when the message was received.</param>
        /// <param name="protocol">The network protocol used.</param>
        /// <param name="isSecure">Whether the connection is secure.</param>
        /// <param name="messageSize">The size of the message in bytes.</param>
        public NetworkMessageMetadata(
            string remoteIp,
            int remotePort,
            DateTimeOffset receivedAt,
            string protocol = "Unknown",
            bool isSecure = false,
            int messageSize = 0)
        {
            RemoteIp = remoteIp;
            RemotePort = remotePort;
            ReceivedAt = receivedAt;
            Protocol = protocol;
            IsSecure = isSecure;
            MessageSize = messageSize;
        }

        /// <summary>
        /// Gets the remote IP address that sent the message.
        /// </summary>
        public string RemoteIp { get; }

        /// <summary>
        /// Gets the remote port number that sent the message.
        /// </summary>
        public int RemotePort { get; }

        /// <summary>
        /// Gets the timestamp when the message was received.
        /// </summary>
        public DateTimeOffset ReceivedAt { get; }

        /// <summary>
        /// Gets the network protocol used (HTTP, UDP, TCP, etc.).
        /// </summary>
        public string Protocol { get; }

        /// <summary>
        /// Gets a value indicating whether the connection is secure (HTTPS, TLS, etc.).
        /// </summary>
        public bool IsSecure { get; }

        /// <summary>
        /// Gets the size of the message in bytes.
        /// </summary>
        public int MessageSize { get; }

        /// <summary>
        /// Gets the remote endpoint as a string.
        /// </summary>
        public string RemoteEndpoint => $"{RemoteIp}:{RemotePort}";

        /// <summary>
        /// Returns a string representation of the metadata.
        /// </summary>
        /// <returns>A string containing the key metadata information.</returns>
        public override string ToString()
        {
            return $"{Protocol} from {RemoteEndpoint} ({MessageSize} bytes) at {ReceivedAt:yyyy-MM-dd HH:mm:ss.fff}";
        }
    }

    /// <summary>
    /// Extension methods for network receivers to provide performance optimization utilities.
    /// </summary>
    public static class NetworkReceiverExtensions
    {
        /// <summary>
        /// Creates an adapter that converts memory-based callbacks to array-based callbacks.
        /// This allows gradual migration from array-based to memory-based APIs.
        /// </summary>
        /// <param name="memoryCallback">The memory-based callback to adapt.</param>
        /// <returns>An array-based callback that internally uses the memory callback.</returns>
        public static Func<byte[], string, int, Task> ToArrayCallback(
            this Func<Memory<byte>, string, int, Task> memoryCallback)
        {
            return (data, ip, port) => memoryCallback(data.AsMemory(), ip, port);
        }

        /// <summary>
        /// Creates an adapter that converts array-based callbacks to memory-based callbacks.
        /// This provides backward compatibility when implementing memory-based receivers.
        /// </summary>
        /// <param name="arrayCallback">The array-based callback to adapt.</param>
        /// <returns>A memory-based callback that internally converts to arrays.</returns>
        /// <remarks>
        /// Note: This adapter will allocate arrays, so it doesn't provide the full performance
        /// benefits of memory-based processing. Use only for compatibility during migration.
        /// </remarks>
        public static Func<Memory<byte>, string, int, Task> ToMemoryCallback(
            this Func<byte[], string, int, Task> arrayCallback)
        {
            return (memory, ip, port) => arrayCallback(memory.ToArray(), ip, port);
        }

        /// <summary>
        /// Measures the performance impact of array allocation vs span usage.
        /// This is a diagnostic utility to help identify performance bottlenecks.
        /// </summary>
        /// <param name="receiver">The network receiver to measure.</param>
        /// <param name="messageCount">The number of test messages to process.</param>
        /// <param name="messageSize">The size of each test message.</param>
        /// <returns>Performance comparison results.</returns>
        public static async Task<PerformanceComparisonResult> MeasureAllocationImpactAsync(
            this IOptimizedNetworkReceiver receiver,
            int messageCount = 1000,
            int messageSize = 1024)
        {
            // This would be implemented to measure the performance difference
            // between array-based and span-based message processing
            await Task.Delay(1); // Placeholder

            return new PerformanceComparisonResult
            {
                ArrayBasedDuration = TimeSpan.FromMilliseconds(100),
                SpanBasedDuration = TimeSpan.FromMilliseconds(50),
                MemoryAllocated = messageCount * messageSize,
                MemorySaved = messageCount * messageSize / 2
            };
        }
    }

    /// <summary>
    /// Results of performance comparison between array-based and span-based processing.
    /// </summary>
    public class PerformanceComparisonResult
    {
        /// <summary>
        /// Gets or sets the duration for array-based processing.
        /// </summary>
        public TimeSpan ArrayBasedDuration { get; set; }

        /// <summary>
        /// Gets or sets the duration for span-based processing.
        /// </summary>
        public TimeSpan SpanBasedDuration { get; set; }

        /// <summary>
        /// Gets or sets the total memory allocated in array-based approach.
        /// </summary>
        public long MemoryAllocated { get; set; }

        /// <summary>
        /// Gets or sets the memory saved by using span-based approach.
        /// </summary>
        public long MemorySaved { get; set; }

        /// <summary>
        /// Gets the performance improvement ratio.
        /// </summary>
        public double PerformanceImprovement =>
            ArrayBasedDuration.TotalMilliseconds / SpanBasedDuration.TotalMilliseconds;

        /// <summary>
        /// Gets the memory savings percentage.
        /// </summary>
        public double MemorySavingsPercentage =>
            MemoryAllocated > 0 ? (double)MemorySaved / MemoryAllocated * 100 : 0;
    }
}
